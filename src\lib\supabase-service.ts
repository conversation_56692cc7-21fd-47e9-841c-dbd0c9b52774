import { supabase } from './supabase'
import { Campaign, Client, CreateCampaignRequest, CreateClientRequest, UpdateCampaignRequest, UpdateClientRequest, PaginatedResponse } from '@/types'
import { Database } from '@/types/database'

type DbClient = Database['public']['Tables']['clients']['Row']
type DbCampaign = Database['public']['Tables']['campaigns']['Row']

// Helper functions to convert between database and app types
function dbClientToClient(dbClient: DbClient): Client {
  return {
    id: dbClient.id,
    name: dbClient.name,
    email: dbClient.email,
    phone: dbClient.phone || '',
    company: dbClient.company,
    industry: dbClient.industry,
    website: dbClient.website || '',
    description: dbClient.description || '',
    status: dbClient.status as 'active' | 'inactive' | 'pending',
    totalBudget: dbClient.total_budget,
    totalSpent: dbClient.total_spent,
    totalCampaigns: dbClient.total_campaigns,
    averageROI: dbClient.average_roi,
    address: dbClient.address as any,
    contactPerson: dbClient.contact_person as any,
    createdAt: dbClient.created_at,
    updatedAt: dbClient.updated_at,
  }
}

function dbCampaignToCampaign(dbCampaign: DbCampaign): Campaign {
  return {
    id: dbCampaign.id,
    name: dbCampaign.name,
    clientId: dbCampaign.client_id,
    status: dbCampaign.status as 'active' | 'paused' | 'completed' | 'draft',
    platform: dbCampaign.platform as 'Facebook' | 'Google' | 'Instagram' | 'LinkedIn' | 'Twitter',
    budget: dbCampaign.budget,
    spent: dbCampaign.spent,
    impressions: dbCampaign.impressions,
    clicks: dbCampaign.clicks,
    conversions: dbCampaign.conversions,
    ctr: dbCampaign.ctr,
    cpc: dbCampaign.cpc,
    conversionRate: dbCampaign.conversion_rate,
    roi: dbCampaign.roi,
    startDate: dbCampaign.start_date,
    endDate: dbCampaign.end_date || undefined,
    targetAudience: dbCampaign.target_audience as any,
    adSets: dbCampaign.ad_sets as any,
    createdAt: dbCampaign.created_at,
    updatedAt: dbCampaign.updated_at,
  }
}

export class SupabaseService {
  // Client operations
  static async getClients(params: {
    page?: number
    limit?: number
    search?: string
    status?: string[]
    industry?: string[]
    sortField?: string
    sortDirection?: 'asc' | 'desc'
    userId: string
  }): Promise<PaginatedResponse<Client>> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      industry,
      sortField = 'updated_at',
      sortDirection = 'desc',
      userId
    } = params

    let query = supabase
      .from('clients')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,company.ilike.%${search}%,email.ilike.%${search}%`)
    }

    if (status && status.length > 0) {
      query = query.in('status', status)
    }

    if (industry && industry.length > 0) {
      query = query.in('industry', industry)
    }

    // Apply sorting
    query = query.order(sortField, { ascending: sortDirection === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    const clients = (data || []).map(dbClientToClient)
    const total = count || 0

    return {
      data: clients,
      pagination: {
        total,
        page,
        limit,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    }
  }

  static async getClientById(id: string, userId: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return dbClientToClient(data)
  }

  static async createClient(clientData: CreateClientRequest, userId: string): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert({
        name: clientData.name,
        email: clientData.email,
        phone: clientData.phone,
        company: clientData.company,
        industry: clientData.industry,
        website: clientData.website,
        description: clientData.description,
        status: 'active',
        total_budget: 0,
        total_spent: 0,
        total_campaigns: 0,
        average_roi: 0,
        address: clientData.address,
        contact_person: clientData.contactPerson,
        user_id: userId,
      })
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return dbClientToClient(data)
  }

  static async updateClient(id: string, clientData: UpdateClientRequest, userId: string): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update({
        name: clientData.name,
        email: clientData.email,
        phone: clientData.phone,
        company: clientData.company,
        industry: clientData.industry,
        website: clientData.website,
        description: clientData.description,
        address: clientData.address,
        contact_person: clientData.contactPerson,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return dbClientToClient(data)
  }

  static async deleteClient(id: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      throw new Error(error.message)
    }
  }

  // Campaign operations
  static async getCampaigns(params: {
    page?: number
    limit?: number
    search?: string
    status?: string[]
    platform?: string[]
    clientId?: string
    sortField?: string
    sortDirection?: 'asc' | 'desc'
    userId: string
  }): Promise<PaginatedResponse<Campaign>> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      platform,
      clientId,
      sortField = 'updated_at',
      sortDirection = 'desc',
      userId
    } = params

    let query = supabase
      .from('campaigns')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)

    // Apply filters
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    if (status && status.length > 0) {
      query = query.in('status', status)
    }

    if (platform && platform.length > 0) {
      query = query.in('platform', platform)
    }

    if (clientId) {
      query = query.eq('client_id', clientId)
    }

    // Apply sorting
    query = query.order(sortField, { ascending: sortDirection === 'asc' })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(error.message)
    }

    const campaigns = (data || []).map(dbCampaignToCampaign)
    const total = count || 0

    return {
      data: campaigns,
      pagination: {
        total,
        page,
        limit,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    }
  }

  static async getCampaignById(id: string, userId: string): Promise<Campaign | null> {
    const { data, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw new Error(error.message)
    }

    return dbCampaignToCampaign(data)
  }

  static async createCampaign(campaignData: CreateCampaignRequest, userId: string): Promise<Campaign> {
    const { data, error } = await supabase
      .from('campaigns')
      .insert({
        name: campaignData.name,
        client_id: campaignData.clientId,
        status: 'draft',
        platform: campaignData.platform,
        budget: campaignData.budget,
        spent: 0,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        ctr: 0,
        cpc: 0,
        conversion_rate: 0,
        roi: 0,
        start_date: campaignData.startDate,
        end_date: campaignData.endDate,
        target_audience: campaignData.targetAudience,
        ad_sets: [],
        user_id: userId,
      })
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return dbCampaignToCampaign(data)
  }

  static async updateCampaign(id: string, campaignData: UpdateCampaignRequest, userId: string): Promise<Campaign> {
    const { data, error } = await supabase
      .from('campaigns')
      .update({
        name: campaignData.name,
        client_id: campaignData.clientId,
        platform: campaignData.platform,
        budget: campaignData.budget,
        start_date: campaignData.startDate,
        end_date: campaignData.endDate,
        target_audience: campaignData.targetAudience,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return dbCampaignToCampaign(data)
  }

  static async deleteCampaign(id: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('campaigns')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      throw new Error(error.message)
    }
  }
}
