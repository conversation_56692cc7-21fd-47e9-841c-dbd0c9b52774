import { NextRequest, NextResponse } from "next/server"
import { DataService } from "@/lib/data-service"
import { AnalyticsQuery } from "@/types"

// GET /api/analytics - Get analytics data with flexible querying
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const startDate = searchParams.get("startDate") || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const endDate = searchParams.get("endDate") || new Date().toISOString().split('T')[0]
    const clientIds = searchParams.get("clientIds")?.split(",") || undefined
    const campaignIds = searchParams.get("campaignIds")?.split(",") || undefined
    const platforms = searchParams.get("platforms")?.split(",") || undefined
    const groupBy = (searchParams.get("groupBy") || "day") as "day" | "week" | "month"
    const metrics = searchParams.get("metrics")?.split(",") || undefined
    
    // Build analytics query
    const query: AnalyticsQuery = {
      startDate,
      endDate,
      clientIds,
      campaignIds,
      platforms,
      groupBy,
      metrics,
    }
    
    // Get analytics data
    const analyticsData = await DataService.getAnalytics(query)
    
    return NextResponse.json({
      success: true,
      data: analyticsData,
      query,
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch analytics data" },
      { status: 500 }
    )
  }
}

// POST /api/analytics - Get analytics data with complex filtering (for dashboard widgets)
export async function POST(request: NextRequest) {
  try {
    const body: AnalyticsQuery = await request.json()
    
    // Validate required fields
    if (!body.startDate || !body.endDate) {
      return NextResponse.json(
        { success: false, error: "Start date and end date are required" },
        { status: 400 }
      )
    }
    
    // Get analytics data
    const analyticsData = await DataService.getAnalytics(body)
    
    return NextResponse.json({
      success: true,
      data: analyticsData,
      query: body,
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch analytics data" },
      { status: 500 }
    )
  }
}
