export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          created_at?: string
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          email: string
          phone: string | null
          company: string
          industry: string
          website: string | null
          description: string | null
          status: string
          total_budget: number
          total_spent: number
          total_campaigns: number
          average_roi: number
          address: Json | null
          contact_person: J<PERSON> | null
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone?: string | null
          company: string
          industry: string
          website?: string | null
          description?: string | null
          status?: string
          total_budget?: number
          total_spent?: number
          total_campaigns?: number
          average_roi?: number
          address?: J<PERSON> | null
          contact_person?: Json | null
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string | null
          company?: string
          industry?: string
          website?: string | null
          description?: string | null
          status?: string
          total_budget?: number
          total_spent?: number
          total_campaigns?: number
          average_roi?: number
          address?: Json | null
          contact_person?: Json | null
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      campaigns: {
        Row: {
          id: string
          name: string
          client_id: string
          status: string
          platform: string
          budget: number
          spent: number
          impressions: number
          clicks: number
          conversions: number
          ctr: number
          cpc: number
          conversion_rate: number
          roi: number
          start_date: string
          end_date: string | null
          target_audience: Json | null
          ad_sets: Json | null
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          client_id: string
          status?: string
          platform: string
          budget: number
          spent?: number
          impressions?: number
          clicks?: number
          conversions?: number
          ctr?: number
          cpc?: number
          conversion_rate?: number
          roi?: number
          start_date: string
          end_date?: string | null
          target_audience?: Json | null
          ad_sets?: Json | null
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          client_id?: string
          status?: string
          platform?: string
          budget?: number
          spent?: number
          impressions?: number
          clicks?: number
          conversions?: number
          ctr?: number
          cpc?: number
          conversion_rate?: number
          roi?: number
          start_date?: string
          end_date?: string | null
          target_audience?: Json | null
          ad_sets?: Json | null
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
