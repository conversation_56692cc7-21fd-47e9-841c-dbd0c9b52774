import { 
  Campaign, 
  Client, 
  KPIData, 
  AnalyticsData,
  AnalyticsQuery,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  CreateClientRequest,
  UpdateClientRequest,
  FilterOptions,
  SortOptions,
  PaginatedResponse,
  ApiResponse
} from "@/types"

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ""

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}/api${endpoint}`
  
  const defaultOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
    },
  }
  
  const response = await fetch(url, { ...defaultOptions, ...options })
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: "Unknown error" }))
    throw new Error(errorData.error || `HTTP ${response.status}`)
  }
  
  return response.json()
}

// Campaign API functions
export const campaignApi = {
  // Get all campaigns with filtering and pagination
  async getAll(params?: {
    page?: number
    limit?: number
    search?: string
    status?: string[]
    platform?: string[]
    clientId?: string
    startDate?: string
    endDate?: string
    sortField?: string
    sortDirection?: "asc" | "desc"
  }): Promise<ApiResponse<PaginatedResponse<Campaign>>> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            searchParams.set(key, value.join(","))
          } else {
            searchParams.set(key, String(value))
          }
        }
      })
    }
    
    return apiRequest(`/campaigns?${searchParams.toString()}`)
  },

  // Get campaign by ID
  async getById(id: string): Promise<ApiResponse<Campaign>> {
    return apiRequest(`/campaigns/${id}`)
  },

  // Create new campaign
  async create(data: CreateCampaignRequest): Promise<ApiResponse<Campaign>> {
    return apiRequest("/campaigns", {
      method: "POST",
      body: JSON.stringify(data),
    })
  },

  // Update campaign
  async update(id: string, data: UpdateCampaignRequest): Promise<ApiResponse<Campaign>> {
    return apiRequest(`/campaigns/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  },

  // Delete campaign
  async delete(id: string): Promise<ApiResponse<void>> {
    return apiRequest(`/campaigns/${id}`, {
      method: "DELETE",
    })
  },

  // Bulk update campaigns
  async bulkUpdate(campaignIds: string[], updates: UpdateCampaignRequest): Promise<ApiResponse<Campaign[]>> {
    return apiRequest("/campaigns", {
      method: "PUT",
      body: JSON.stringify({ campaignIds, updates }),
    })
  },

  // Bulk delete campaigns
  async bulkDelete(campaignIds: string[]): Promise<ApiResponse<void>> {
    return apiRequest("/campaigns", {
      method: "DELETE",
      body: JSON.stringify({ campaignIds }),
    })
  },
}

// Client API functions
export const clientApi = {
  // Get all clients with filtering and pagination
  async getAll(params?: {
    page?: number
    limit?: number
    search?: string
    status?: string[]
    sortField?: string
    sortDirection?: "asc" | "desc"
  }): Promise<ApiResponse<PaginatedResponse<Client>>> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            searchParams.set(key, value.join(","))
          } else {
            searchParams.set(key, String(value))
          }
        }
      })
    }
    
    return apiRequest(`/clients?${searchParams.toString()}`)
  },

  // Get client by ID
  async getById(id: string): Promise<ApiResponse<Client>> {
    return apiRequest(`/clients/${id}`)
  },

  // Create new client
  async create(data: CreateClientRequest): Promise<ApiResponse<Client>> {
    return apiRequest("/clients", {
      method: "POST",
      body: JSON.stringify(data),
    })
  },

  // Update client
  async update(id: string, data: UpdateClientRequest): Promise<ApiResponse<Client>> {
    return apiRequest(`/clients/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  },

  // Delete client
  async delete(id: string): Promise<ApiResponse<void>> {
    return apiRequest(`/clients/${id}`, {
      method: "DELETE",
    })
  },

  // Bulk update clients
  async bulkUpdate(clientIds: string[], updates: UpdateClientRequest): Promise<ApiResponse<Client[]>> {
    return apiRequest("/clients", {
      method: "PUT",
      body: JSON.stringify({ clientIds, updates }),
    })
  },

  // Bulk delete clients
  async bulkDelete(clientIds: string[]): Promise<ApiResponse<void>> {
    return apiRequest("/clients", {
      method: "DELETE",
      body: JSON.stringify({ clientIds }),
    })
  },
}

// Analytics API functions
export const analyticsApi = {
  // Get analytics data
  async getData(query: AnalyticsQuery): Promise<ApiResponse<AnalyticsData[]>> {
    return apiRequest("/analytics", {
      method: "POST",
      body: JSON.stringify(query),
    })
  },

  // Get analytics data with URL parameters (for simple queries)
  async getDataSimple(params?: {
    startDate?: string
    endDate?: string
    clientIds?: string[]
    campaignIds?: string[]
    platforms?: string[]
    groupBy?: "day" | "week" | "month"
    metrics?: string[]
  }): Promise<ApiResponse<AnalyticsData[]>> {
    const searchParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            searchParams.set(key, value.join(","))
          } else {
            searchParams.set(key, String(value))
          }
        }
      })
    }
    
    return apiRequest(`/analytics?${searchParams.toString()}`)
  },
}

// KPI API functions
export const kpiApi = {
  // Get current KPI data
  async getData(): Promise<ApiResponse<KPIData>> {
    return apiRequest("/kpi")
  },
}

// Export all APIs
export const api = {
  campaigns: campaignApi,
  clients: clientApi,
  analytics: analyticsApi,
  kpi: kpiApi,
}
