import { NextRequest, NextResponse } from "next/server"
import { DataService } from "@/lib/data-service"

// GET /api/kpi - Get current KPI data
export async function GET(request: NextRequest) {
  try {
    const kpiData = await DataService.getKPIData()
    
    return NextResponse.json({
      success: true,
      data: kpiData,
    })
  } catch (error) {
    console.error("Error fetching KPI data:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch KPI data" },
      { status: 500 }
    )
  }
}
