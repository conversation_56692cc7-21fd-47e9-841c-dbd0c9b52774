"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, DollarSign, Target, Users } from "lucide-react"
import { Campaign, Client, CreateCampaignRequest, UpdateCampaignRequest } from "@/types"
import { api } from "@/lib/api-client"

// Form validation schema
const campaignSchema = z.object({
  name: z.string().min(1, "Campaign name is required").max(100, "Name too long"),
  clientId: z.string().min(1, "Client is required"),
  budget: z.number().min(1, "Budget must be greater than 0"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().min(1, "End date is required"),
  platform: z.string().min(1, "Platform is required"),
  objective: z.enum(["awareness", "traffic", "engagement", "leads", "sales", "app_installs"]),
  description: z.string().optional(),
  targetAudience: z.object({
    ageRange: z.string().optional(),
    gender: z.enum(["All", "Male", "Female"]).optional(),
    location: z.array(z.string()).optional(),
    interests: z.array(z.string()).optional(),
  }).optional(),
})

type CampaignFormData = z.infer<typeof campaignSchema>

interface CampaignFormProps {
  campaign?: Campaign
  onSubmit: (data: CreateCampaignRequest | UpdateCampaignRequest) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const platforms = [
  "Google Ads",
  "Facebook Ads",
  "Instagram Ads",
  "LinkedIn Ads",
  "Twitter Ads",
  "TikTok Ads",
  "YouTube Ads",
  "Pinterest Ads",
]

const objectives = [
  { value: "awareness", label: "Brand Awareness" },
  { value: "traffic", label: "Website Traffic" },
  { value: "engagement", label: "Engagement" },
  { value: "leads", label: "Lead Generation" },
  { value: "sales", label: "Sales/Conversions" },
  { value: "app_installs", label: "App Installs" },
]

const ageRanges = [
  "18-24", "25-34", "35-44", "45-54", "55-64", "65+"
]

const commonInterests = [
  "Technology", "Marketing", "Business", "Finance", "Healthcare", 
  "Education", "Entertainment", "Sports", "Travel", "Food & Dining",
  "Fashion", "Automotive", "Real Estate", "Fitness", "Gaming"
]

export function CampaignForm({ campaign, onSubmit, onCancel, isLoading }: CampaignFormProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loadingClients, setLoadingClients] = useState(true)
  const [selectedInterests, setSelectedInterests] = useState<string[]>(
    campaign?.targetAudience?.interests || []
  )
  const [selectedLocations, setSelectedLocations] = useState<string[]>(
    campaign?.targetAudience?.location || []
  )

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: campaign?.name || "",
      clientId: campaign?.clientId || "",
      budget: campaign?.budget || 0,
      startDate: campaign?.startDate || "",
      endDate: campaign?.endDate || "",
      platform: campaign?.platform || "",
      objective: campaign?.objective || "awareness",
      description: campaign?.description || "",
      targetAudience: {
        ageRange: campaign?.targetAudience?.ageRange || "",
        gender: campaign?.targetAudience?.gender || "All",
        location: campaign?.targetAudience?.location || [],
        interests: campaign?.targetAudience?.interests || [],
      },
    },
  })

  // Load clients on component mount
  useEffect(() => {
    const loadClients = async () => {
      try {
        const response = await api.clients.getAll({ limit: 100 })
        if (response.success) {
          setClients(response.data.data)
        }
      } catch (error) {
        console.error("Failed to load clients:", error)
      } finally {
        setLoadingClients(false)
      }
    }
    loadClients()
  }, [])

  const handleSubmit = async (data: CampaignFormData) => {
    const formData = {
      ...data,
      targetAudience: {
        ...data.targetAudience,
        location: selectedLocations,
        interests: selectedInterests,
      },
    }
    await onSubmit(formData)
  }

  const addInterest = (interest: string) => {
    if (!selectedInterests.includes(interest)) {
      const newInterests = [...selectedInterests, interest]
      setSelectedInterests(newInterests)
      form.setValue("targetAudience.interests", newInterests)
    }
  }

  const removeInterest = (interest: string) => {
    const newInterests = selectedInterests.filter(i => i !== interest)
    setSelectedInterests(newInterests)
    form.setValue("targetAudience.interests", newInterests)
  }

  const addLocation = (location: string) => {
    if (location && !selectedLocations.includes(location)) {
      const newLocations = [...selectedLocations, location]
      setSelectedLocations(newLocations)
      form.setValue("targetAudience.location", newLocations)
    }
  }

  const removeLocation = (location: string) => {
    const newLocations = selectedLocations.filter(l => l !== location)
    setSelectedLocations(newLocations)
    form.setValue("targetAudience.location", newLocations)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter campaign name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a client" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingClients ? (
                          <SelectItem value="loading" disabled>Loading clients...</SelectItem>
                        ) : (
                          (clients || []).map((client) => (
                            <SelectItem key={client.id} value={client.id}>
                              {client.name} - {client.company}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="platform"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Platform</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select platform" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {platforms.map((platform) => (
                          <SelectItem key={platform} value={platform}>
                            {platform}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="objective"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Objective</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select objective" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {objectives.map((objective) => (
                          <SelectItem key={objective.value} value={objective.value}>
                            {objective.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Campaign description (optional)" 
                        className="resize-none" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Budget and Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Budget & Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget ($)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="0" 
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Total campaign budget in USD
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </div>

        {/* Target Audience */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Target Audience
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="targetAudience.ageRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Age Range</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select age range" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {(ageRanges || []).map((range) => (
                          <SelectItem key={range} value={range}>
                            {range}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="targetAudience.gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="All">All</SelectItem>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Interests */}
            <div>
              <Label>Interests</Label>
              <div className="mt-2 space-y-2">
                <Select onValueChange={addInterest}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add interests" />
                  </SelectTrigger>
                  <SelectContent>
                    {commonInterests
                      .filter(interest => !selectedInterests.includes(interest))
                      .map((interest) => (
                        <SelectItem key={interest} value={interest}>
                          {interest}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                {selectedInterests.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {selectedInterests.map((interest) => (
                      <Badge 
                        key={interest} 
                        variant="secondary" 
                        className="cursor-pointer"
                        onClick={() => removeInterest(interest)}
                      >
                        {interest} ×
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Locations */}
            <div>
              <Label>Target Locations</Label>
              <div className="mt-2 space-y-2">
                <div className="flex gap-2">
                  <Input 
                    placeholder="Add location (e.g., United States, California)"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addLocation(e.currentTarget.value)
                        e.currentTarget.value = ''
                      }
                    }}
                  />
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={(e) => {
                      const input = e.currentTarget.previousElementSibling as HTMLInputElement
                      addLocation(input.value)
                      input.value = ''
                    }}
                  >
                    Add
                  </Button>
                </div>
                {selectedLocations.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {(selectedLocations || []).map((location) => (
                      <Badge
                        key={location}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => removeLocation(location)}
                      >
                        {location} ×
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : campaign ? "Update Campaign" : "Create Campaign"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
