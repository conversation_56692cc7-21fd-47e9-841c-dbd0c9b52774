"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { CampaignsTable } from "@/components/tables/campaigns-table"
import { CampaignModal } from "@/components/modals/campaign-modal"
import { Campaign, Client } from "@/types"
import { api } from "@/lib/api-client"

export default function CampaignsPage() {
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | undefined>()
  const [selectedClient, setSelectedClient] = useState<Client | undefined>()
  const [modalMode, setModalMode] = useState<"create" | "edit" | "view">("create")
  const [modalOpen, setModalOpen] = useState(false)
  const [clients, setClients] = useState<Client[]>([])
  const [refreshKey, setRefreshKey] = useState(0)

  // Load clients for campaign details
  useEffect(() => {
    const loadClients = async () => {
      try {
        const response = await api.clients.getAll({ limit: 100 })
        if (response.success && response.data) {
          setClients(response.data.data || [])
        }
      } catch (error) {
        console.error("Failed to load clients:", error)
      }
    }
    loadClients()
  }, [])

  const handleCreateCampaign = () => {
    setSelectedCampaign(undefined)
    setSelectedClient(undefined)
    setModalMode("create")
    setModalOpen(true)
  }

  const handleEditCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign)
    setSelectedClient(clients.find(c => c.id === campaign.clientId))
    setModalMode("edit")
    setModalOpen(true)
  }

  const handleViewCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign)
    setSelectedClient(clients.find(c => c.id === campaign.clientId))
    setModalMode("view")
    setModalOpen(true)
  }

  const handleDeleteCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign)
    setSelectedClient(clients.find(c => c.id === campaign.clientId))
    setModalMode("view")
    setModalOpen(true)
  }

  const handleModalSuccess = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Campaign Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Create, manage, and monitor your advertising campaigns
          </p>
        </div>

        {/* Campaigns Table */}
        <CampaignsTable
          key={refreshKey}
          onCreateCampaign={handleCreateCampaign}
          onEditCampaign={handleEditCampaign}
          onViewCampaign={handleViewCampaign}
          onDeleteCampaign={handleDeleteCampaign}
        />

        {/* Campaign Modal */}
        <CampaignModal
          campaign={selectedCampaign}
          client={selectedClient}
          mode={modalMode}
          open={modalOpen}
          onOpenChange={setModalOpen}
          onSuccess={handleModalSuccess}
        />
      </div>
    </DashboardLayout>
  )
}
