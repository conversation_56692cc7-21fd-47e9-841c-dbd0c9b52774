import { 
  Campaign, 
  Client, 
  ChartData, 
  KPIData, 
  AnalyticsData,
  AnalyticsQuery,
  FilterOptions,
  SortOptions,
  PaginatedResponse,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  CreateClientRequest,
  UpdateClientRequest
} from "@/types"

// In-memory data store (simulating a database)
class DataStore {
  private campaigns: Campaign[] = []
  private clients: Client[] = []
  private analytics: ChartData[] = []

  // Initialize with mock data
  init(campaigns: Campaign[], clients: Client[], analytics: ChartData[]) {
    this.campaigns = [...campaigns]
    this.clients = [...clients]
    this.analytics = [...analytics]
  }

  // Campaign operations
  getCampaigns(): Campaign[] {
    return [...this.campaigns]
  }

  getCampaignById(id: string): Campaign | undefined {
    return this.campaigns.find(c => c.id === id)
  }

  createCampaign(data: CreateCampaignRequest): Campaign {
    const campaign: Campaign = {
      id: `campaign-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...data,
      status: 'draft',
      spent: 0,
      impressions: 0,
      clicks: 0,
      conversions: 0,
      ctr: 0,
      cpc: 0,
      conversionRate: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    this.campaigns.push(campaign)
    return campaign
  }

  updateCampaign(id: string, data: UpdateCampaignRequest): Campaign | null {
    const index = this.campaigns.findIndex(c => c.id === id)
    if (index === -1) return null

    this.campaigns[index] = {
      ...this.campaigns[index],
      ...data,
      updatedAt: new Date().toISOString(),
    }
    return this.campaigns[index]
  }

  deleteCampaign(id: string): boolean {
    const index = this.campaigns.findIndex(c => c.id === id)
    if (index === -1) return false
    this.campaigns.splice(index, 1)
    return true
  }

  // Client operations
  getClients(): Client[] {
    return [...this.clients]
  }

  getClientById(id: string): Client | undefined {
    return this.clients.find(c => c.id === id)
  }

  createClient(data: CreateClientRequest): Client {
    const client: Client = {
      id: `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...data,
      status: 'active',
      totalSpent: 0,
      totalCampaigns: 0,
      averageROI: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    this.clients.push(client)
    return client
  }

  updateClient(id: string, data: UpdateClientRequest): Client | null {
    const index = this.clients.findIndex(c => c.id === id)
    if (index === -1) return null

    this.clients[index] = {
      ...this.clients[index],
      ...data,
      updatedAt: new Date().toISOString(),
    }
    return this.clients[index]
  }

  deleteClient(id: string): boolean {
    const index = this.clients.findIndex(c => c.id === id)
    if (index === -1) return false
    this.clients.splice(index, 1)
    return true
  }

  // Analytics operations
  getAnalytics(): ChartData[] {
    return [...this.analytics]
  }

  getAnalyticsByDateRange(startDate: string, endDate: string): ChartData[] {
    return this.analytics.filter(data => 
      data.date >= startDate && data.date <= endDate
    )
  }
}

// Singleton instance
const dataStore = new DataStore()

// Data service functions
export class DataService {
  static initializeData(campaigns: Campaign[], clients: Client[], analytics: ChartData[]) {
    dataStore.init(campaigns, clients, analytics)
  }

  // Campaign services
  static async getCampaigns(
    filters?: FilterOptions,
    sort?: SortOptions,
    page = 1,
    limit = 10
  ): Promise<PaginatedResponse<Campaign>> {
    let campaigns = dataStore.getCampaigns()

    // Apply filters
    if (filters) {
      if (filters.search) {
        const search = filters.search.toLowerCase()
        campaigns = campaigns.filter(c => 
          c.name.toLowerCase().includes(search) ||
          c.platform.toLowerCase().includes(search)
        )
      }
      if (filters.status?.length) {
        campaigns = campaigns.filter(c => filters.status!.includes(c.status))
      }
      if (filters.platform?.length) {
        campaigns = campaigns.filter(c => filters.platform!.includes(c.platform))
      }
      if (filters.clientId) {
        campaigns = campaigns.filter(c => c.clientId === filters.clientId)
      }
      if (filters.dateRange) {
        campaigns = campaigns.filter(c => 
          c.startDate >= filters.dateRange!.start && 
          c.endDate <= filters.dateRange!.end
        )
      }
    }

    // Apply sorting
    if (sort) {
      campaigns.sort((a, b) => {
        const aVal = (a as any)[sort.field]
        const bVal = (b as any)[sort.field]
        const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0
        return sort.direction === 'desc' ? -comparison : comparison
      })
    }

    // Apply pagination
    const total = campaigns.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedCampaigns = campaigns.slice(startIndex, endIndex)

    return {
      data: paginatedCampaigns,
      total,
      page,
      limit,
      hasNext: endIndex < total,
      hasPrev: page > 1,
    }
  }

  static async getCampaignById(id: string): Promise<Campaign | null> {
    return dataStore.getCampaignById(id) || null
  }

  static async createCampaign(data: CreateCampaignRequest): Promise<Campaign> {
    return dataStore.createCampaign(data)
  }

  static async updateCampaign(id: string, data: UpdateCampaignRequest): Promise<Campaign | null> {
    return dataStore.updateCampaign(id, data)
  }

  static async deleteCampaign(id: string): Promise<boolean> {
    return dataStore.deleteCampaign(id)
  }

  // Client services
  static async getClients(
    filters?: FilterOptions,
    sort?: SortOptions,
    page = 1,
    limit = 10
  ): Promise<PaginatedResponse<Client>> {
    let clients = dataStore.getClients()

    // Apply filters
    if (filters) {
      if (filters.search) {
        const search = filters.search.toLowerCase()
        clients = clients.filter(c => 
          c.name.toLowerCase().includes(search) ||
          c.company.toLowerCase().includes(search) ||
          c.industry.toLowerCase().includes(search)
        )
      }
      if (filters.status?.length) {
        clients = clients.filter(c => filters.status!.includes(c.status))
      }
    }

    // Apply sorting
    if (sort) {
      clients.sort((a, b) => {
        const aVal = (a as any)[sort.field]
        const bVal = (b as any)[sort.field]
        const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0
        return sort.direction === 'desc' ? -comparison : comparison
      })
    }

    // Apply pagination
    const total = clients.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedClients = clients.slice(startIndex, endIndex)

    return {
      data: paginatedClients,
      total,
      page,
      limit,
      hasNext: endIndex < total,
      hasPrev: page > 1,
    }
  }

  static async getClientById(id: string): Promise<Client | null> {
    return dataStore.getClientById(id) || null
  }

  static async createClient(data: CreateClientRequest): Promise<Client> {
    return dataStore.createClient(data)
  }

  static async updateClient(id: string, data: UpdateClientRequest): Promise<Client | null> {
    return dataStore.updateClient(id, data)
  }

  static async deleteClient(id: string): Promise<boolean> {
    return dataStore.deleteClient(id)
  }

  // Analytics services
  static async getAnalytics(query: AnalyticsQuery): Promise<AnalyticsData[]> {
    const analytics = dataStore.getAnalyticsByDateRange(query.startDate, query.endDate)
    
    // Group data based on groupBy parameter
    const groupedData: { [key: string]: ChartData[] } = {}
    
    analytics.forEach(data => {
      let key = data.date
      if (query.groupBy === 'week') {
        const date = new Date(data.date)
        const weekStart = new Date(date.setDate(date.getDate() - date.getDay()))
        key = weekStart.toISOString().split('T')[0]
      } else if (query.groupBy === 'month') {
        key = data.date.substring(0, 7) // YYYY-MM
      }
      
      if (!groupedData[key]) {
        groupedData[key] = []
      }
      groupedData[key].push(data)
    })

    // Aggregate data for each period
    return Object.entries(groupedData).map(([period, data]) => {
      const totals = data.reduce((acc, curr) => ({
        impressions: acc.impressions + curr.impressions,
        clicks: acc.clicks + curr.clicks,
        conversions: acc.conversions + curr.conversions,
        spent: acc.spent + curr.spent,
        revenue: acc.revenue + curr.revenue,
      }), { impressions: 0, clicks: 0, conversions: 0, spent: 0, revenue: 0 })

      const metrics = {
        ...totals,
        ctr: totals.clicks / totals.impressions * 100,
        cpc: totals.spent / totals.clicks,
        conversionRate: totals.conversions / totals.clicks * 100,
        roas: totals.revenue / totals.spent,
      }

      return {
        period,
        metrics,
      }
    }).sort((a, b) => a.period.localeCompare(b.period))
  }

  static async getKPIData(): Promise<KPIData> {
    const campaigns = dataStore.getCampaigns()
    const analytics = dataStore.getAnalytics()

    const totals = analytics.reduce((acc, curr) => ({
      impressions: acc.impressions + curr.impressions,
      clicks: acc.clicks + curr.clicks,
      conversions: acc.conversions + curr.conversions,
      spent: acc.spent + curr.spent,
      revenue: acc.revenue + curr.revenue,
    }), { impressions: 0, clicks: 0, conversions: 0, spent: 0, revenue: 0 })

    return {
      totalCampaigns: campaigns.length,
      totalSpent: totals.spent,
      totalImpressions: totals.impressions,
      totalClicks: totals.clicks,
      totalConversions: totals.conversions,
      averageCTR: totals.clicks / totals.impressions * 100,
      averageCPC: totals.spent / totals.clicks,
      averageConversionRate: totals.conversions / totals.clicks * 100,
      revenue: totals.revenue,
    }
  }
}
