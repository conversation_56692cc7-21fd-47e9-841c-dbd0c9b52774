"use client"

import React from "react"
import { Home, BarChart3, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { FloatingNav } from "@/components/ui/floating-navbar"

const navItems = [
  {
    name: "Dashboard",
    link: "/",
    icon: <Home className="h-4 w-4" />,
  },
  {
    name: "Analytics",
    link: "/analytics",
    icon: <BarChart3 className="h-4 w-4" />,
  },
  {
    name: "Clients",
    link: "/clients",
    icon: <Users className="h-4 w-4" />,
  },
  {
    name: "AI Insights",
    link: "/insights",
    icon: <Brain className="h-4 w-4" />,
  },
  {
    name: "Settings",
    link: "/settings",
    icon: <Settings className="h-4 w-4" />,
  },
]

export function FloatingNavigation() {
  return <FloatingNav navItems={navItems} />
}
