"use client"

import React from "react"
import { motion } from "framer-motion"
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface KPICardProps {
  title: string
  value: string | number
  change?: {
    value: number
    period: string
    isPositive?: boolean
  }
  icon?: LucideIcon
  iconColor?: string
  trend?: "up" | "down" | "neutral"
  loading?: boolean
  className?: string
  onClick?: () => void
}

export function KPICard({
  title,
  value,
  change,
  icon: Icon,
  iconColor = "text-purple-600",
  trend,
  loading = false,
  className,
  onClick,
}: KPICardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === "number") {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`
      }
      if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`
      }
      return val.toLocaleString()
    }
    return val
  }

  const getTrendIcon = () => {
    if (change?.isPositive !== undefined) {
      return change.isPositive ? TrendingUp : TrendingDown
    }
    if (trend === "up") return TrendingUp
    if (trend === "down") return TrendingDown
    return null
  }

  const getTrendColor = () => {
    if (change?.isPositive !== undefined) {
      return change.isPositive ? "text-green-600" : "text-red-600"
    }
    if (trend === "up") return "text-green-600"
    if (trend === "down") return "text-red-600"
    return "text-gray-500"
  }

  const TrendIcon = getTrendIcon()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -2 }}
      className={cn("cursor-pointer", className)}
      onClick={onClick}
    >
      <Card className="p-6 hover:shadow-lg transition-all duration-200 border-0 bg-white/80 backdrop-blur dark:bg-gray-900/80">
        <div className="flex items-center justify-between space-y-0 pb-2">
          <h3 className="tracking-tight text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </h3>
          {Icon && (
            <Icon className={cn("h-5 w-5", iconColor)} />
          )}
        </div>
        
        <div className="space-y-1">
          {loading ? (
            <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          ) : (
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {formatValue(value)}
            </div>
          )}
          
          {change && !loading && (
            <div className="flex items-center space-x-1">
              {TrendIcon && (
                <TrendIcon className={cn("h-3 w-3", getTrendColor())} />
              )}
              <p className={cn("text-xs font-medium", getTrendColor())}>
                {change.isPositive !== undefined ? (change.isPositive ? "+" : "") : ""}
                {change.value}% {change.period}
              </p>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  )
}

// Preset KPI card variants for common metrics
export function CampaignsKPICard({ value, change, loading, onClick }: Omit<KPICardProps, "title" | "icon">) {
  return (
    <KPICard
      title="Total Campaigns"
      value={value}
      change={change}
      icon={TrendingUp}
      iconColor="text-blue-600"
      loading={loading}
      onClick={onClick}
    />
  )
}

export function RevenueKPICard({ value, change, loading, onClick }: Omit<KPICardProps, "title" | "icon">) {
  return (
    <KPICard
      title="Total Revenue"
      value={value}
      change={change}
      icon={TrendingUp}
      iconColor="text-green-600"
      loading={loading}
      onClick={onClick}
    />
  )
}

export function ConversionsKPICard({ value, change, loading, onClick }: Omit<KPICardProps, "title" | "icon">) {
  return (
    <KPICard
      title="Conversions"
      value={value}
      change={change}
      icon={TrendingUp}
      iconColor="text-purple-600"
      loading={loading}
      onClick={onClick}
    />
  )
}

export function ClientsKPICard({ value, change, loading, onClick }: Omit<KPICardProps, "title" | "icon">) {
  return (
    <KPICard
      title="Active Clients"
      value={value}
      change={change}
      icon={TrendingUp}
      iconColor="text-orange-600"
      loading={loading}
      onClick={onClick}
    />
  )
}
