import { NextRequest, NextResponse } from "next/server"
import { SupabaseService } from "@/lib/supabase-service"
import { isSupabaseConfigured } from "@/lib/supabase"
import { DataService } from "@/lib/data-service"
import { CreateClientRequest, FilterOptions, SortOptions } from "@/types"
import { getServerUser } from "@/lib/auth-server"

// GET /api/clients - Get all clients with filtering, sorting, and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || undefined
    const status = searchParams.get("status")?.split(",").filter(Boolean) || undefined
    const industry = searchParams.get("industry")?.split(",").filter(Boolean) || undefined
    const sortField = searchParams.get("sortField") || "updatedAt"
    const sortDirection = (searchParams.get("sortDirection") || "desc") as "asc" | "desc"

    if (isSupabaseConfigured) {
      // Use Supabase
      const user = await getServerUser(request)
      const result = await SupabaseService.getClients({
        page,
        limit,
        search,
        status,
        industry,
        sortField: sortField === 'updatedAt' ? 'updated_at' : sortField,
        sortDirection,
        userId: user.id,
      })

      return NextResponse.json({
        success: true,
        data: result,
      })
    } else {
      // Use mock data service
      const filterOptions: FilterOptions = {
        status: status?.length ? status : undefined,
        industry: industry?.length ? industry : undefined,
      }

      const sortOptions: SortOptions = {
        field: sortField,
        direction: sortDirection,
      }

      const result = await DataService.getClients(filterOptions, sortOptions, page, limit)

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
        },
      })
    }
  } catch (error) {
    console.error("Error fetching clients:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch clients" },
      { status: 500 }
    )
  }
}

// POST /api/clients - Create a new client
export async function POST(request: NextRequest) {
  try {
    const body: CreateClientRequest = await request.json()

    // Validate required fields
    if (!body.name || !body.email || !body.company || !body.industry) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      )
    }

    let client
    if (isSupabaseConfigured) {
      // Use Supabase
      const user = await getServerUser(request)
      client = await SupabaseService.createClient(body, user.id)
    } else {
      // Use mock data service
      client = await DataService.createClient(body)
    }

    return NextResponse.json({
      success: true,
      data: client,
      message: "Client created successfully",
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating client:", error)
    return NextResponse.json(
      { success: false, error: "Failed to create client" },
      { status: 500 }
    )
  }
}

// PUT /api/clients - Bulk update clients
export async function PUT(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request)
    const body = await request.json()
    const { clientIds, updates } = body

    if (!clientIds || !Array.isArray(clientIds) || !updates) {
      return NextResponse.json(
        { success: false, error: "Invalid request body" },
        { status: 400 }
      )
    }

    const updatedClients = []
    for (const id of clientIds) {
      try {
        const client = await SupabaseService.updateClient(id, updates, user.id)
        updatedClients.push(client)
      } catch (error) {
        console.error(`Failed to update client ${id}:`, error)
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedClients,
      message: `${updatedClients.length} clients updated successfully`,
    })
  } catch (error) {
    console.error("Error bulk updating clients:", error)
    return NextResponse.json(
      { success: false, error: "Failed to update clients" },
      { status: 500 }
    )
  }
}

// DELETE /api/clients - Bulk delete clients
export async function DELETE(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request)
    const body = await request.json()
    const { clientIds } = body

    if (!clientIds || !Array.isArray(clientIds)) {
      return NextResponse.json(
        { success: false, error: "Invalid request body" },
        { status: 400 }
      )
    }

    let deletedCount = 0
    for (const id of clientIds) {
      try {
        await SupabaseService.deleteClient(id, user.id)
        deletedCount++
      } catch (error) {
        console.error(`Failed to delete client ${id}:`, error)
      }
    }

    return NextResponse.json({
      success: true,
      message: `${deletedCount} clients deleted successfully`,
    })
  } catch (error) {
    console.error("Error bulk deleting clients:", error)
    return NextResponse.json(
      { success: false, error: "Failed to delete clients" },
      { status: 500 }
    )
  }
}
