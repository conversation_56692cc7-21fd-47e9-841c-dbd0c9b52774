import { KPIData, ChartData, Campaign, Client } from "@/types"

// Data generators
export class MockDataGenerator {
  private static readonly INDUSTRIES = [
    "Technology", "Healthcare", "Finance", "E-commerce", "Education",
    "Real Estate", "Fashion & Retail", "Food & Beverage", "Travel", "Automotive"
  ]

  private static readonly PLATFORMS: Campaign['platform'][] = [
    "Google Ads", "Facebook", "Instagram", "LinkedIn", "Twitter", "TikTok"
  ]

  private static readonly OBJECTIVES: Campaign['objective'][] = [
    "awareness", "traffic", "engagement", "leads", "sales", "app_installs"
  ]

  private static readonly CAMPAIGN_NAMES = [
    "Summer Sale 2024", "Brand Awareness Q1", "Product Launch Campaign",
    "Holiday Special", "Back to School", "Black Friday Deals", "New Year Promotion",
    "Spring Collection", "Customer Acquisition", "Retargeting Campaign",
    "Lead Generation", "App Download Drive", "Video Marketing Push", "Social Media Boost"
  ]

  private static readonly COMPANY_NAMES = [
    "TechStart Inc.", "Fashion Forward LLC", "HealthWise Solutions", "EduTech Pro",
    "GreenEnergy Corp", "FinanceFirst", "RetailMax", "FoodieDelight", "TravelEase",
    "AutoDrive", "SmartHome Solutions", "CloudTech Systems", "DataAnalytics Pro"
  ]

  static generateClients(count: number = 10): Client[] {
    const clients: Client[] = []

    for (let i = 0; i < count; i++) {
      const companyName = this.COMPANY_NAMES[i % this.COMPANY_NAMES.length]
      const industry = this.INDUSTRIES[Math.floor(Math.random() * this.INDUSTRIES.length)]
      const totalBudget = Math.floor(Math.random() * 100000) + 10000
      const totalSpent = Math.floor(totalBudget * (0.3 + Math.random() * 0.6))
      const totalCampaigns = Math.floor(Math.random() * 15) + 1

      clients.push({
        id: `client-${i + 1}`,
        name: `${companyName.split(' ')[0]} Team`,
        email: `contact@${companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
        phone: `+1 (555) ${String(Math.floor(Math.random() * 900) + 100)}-${String(Math.floor(Math.random() * 9000) + 1000)}`,
        company: companyName,
        industry,
        status: Math.random() > 0.1 ? 'active' : 'inactive',
        totalBudget,
        totalSpent,
        totalCampaigns,
        averageROI: Math.round((Math.random() * 4 + 1) * 10) / 10,
        address: {
          street: `${Math.floor(Math.random() * 9999) + 1} ${['Main', 'Oak', 'Pine', 'Elm', 'Cedar'][Math.floor(Math.random() * 5)]} St`,
          city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][Math.floor(Math.random() * 5)],
          state: ['NY', 'CA', 'IL', 'TX', 'AZ'][Math.floor(Math.random() * 5)],
          country: 'USA',
          zipCode: String(Math.floor(Math.random() * 90000) + 10000)
        },
        contactPerson: {
          name: `${['John', 'Jane', 'Mike', 'Sarah', 'David'][Math.floor(Math.random() * 5)]} ${['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'][Math.floor(Math.random() * 5)]}`,
          email: `contact@${companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
          phone: `+1 (555) ${String(Math.floor(Math.random() * 900) + 100)}-${String(Math.floor(Math.random() * 9000) + 1000)}`,
          role: ['CEO', 'Marketing Director', 'CMO', 'Marketing Manager'][Math.floor(Math.random() * 4)]
        },
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      })
    }

    return clients
  }

  static generateCampaigns(clients: Client[], count: number = 25): Campaign[] {
    const campaigns: Campaign[] = []

    for (let i = 0; i < count; i++) {
      const client = clients[Math.floor(Math.random() * clients.length)]
      const platform = this.PLATFORMS[Math.floor(Math.random() * this.PLATFORMS.length)]
      const objective = this.OBJECTIVES[Math.floor(Math.random() * this.OBJECTIVES.length)]
      const budget = Math.floor(Math.random() * 50000) + 5000
      const spent = Math.floor(budget * (0.1 + Math.random() * 0.8))
      const impressions = Math.floor(Math.random() * 500000) + 50000
      const clicks = Math.floor(impressions * (0.01 + Math.random() * 0.08))
      const conversions = Math.floor(clicks * (0.005 + Math.random() * 0.05))

      const startDate = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000)
      const endDate = new Date(startDate.getTime() + (Math.random() * 90 + 30) * 24 * 60 * 60 * 1000)

      campaigns.push({
        id: `campaign-${i + 1}`,
        name: `${this.CAMPAIGN_NAMES[i % this.CAMPAIGN_NAMES.length]} - ${client.company}`,
        clientId: client.id,
        status: ['active', 'paused', 'completed', 'draft'][Math.floor(Math.random() * 4)] as Campaign['status'],
        budget,
        spent,
        impressions,
        clicks,
        conversions,
        ctr: Math.round((clicks / impressions) * 10000) / 100,
        cpc: Math.round((spent / clicks) * 100) / 100,
        cpm: Math.round((spent / impressions) * 1000 * 100) / 100,
        conversionRate: Math.round((conversions / clicks) * 10000) / 100,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        platform,
        objective,
        targetAudience: {
          ageRange: ['18-24', '25-34', '35-44', '45-54', '55+'][Math.floor(Math.random() * 5)],
          gender: ['All', 'Male', 'Female'][Math.floor(Math.random() * 3)],
          location: ['United States', 'Canada', 'United Kingdom', 'Australia'][Math.floor(Math.random() * 4)].split(','),
          interests: ['Technology', 'Sports', 'Fashion', 'Travel', 'Food'].slice(0, Math.floor(Math.random() * 3) + 1)
        },
        createdAt: startDate.toISOString(),
        updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      })
    }

    return campaigns
  }

  static generateAnalyticsData(days: number = 90): ChartData[] {
    const data: ChartData[] = []
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + i)

      // Add some seasonality and trends
      const dayOfWeek = date.getDay()
      const weekendMultiplier = dayOfWeek === 0 || dayOfWeek === 6 ? 0.7 : 1
      const trendMultiplier = 1 + (i / days) * 0.3 // 30% growth over period

      const baseImpressions = 45000 + Math.random() * 20000
      const impressions = Math.floor(baseImpressions * weekendMultiplier * trendMultiplier)
      const clicks = Math.floor(impressions * (0.03 + Math.random() * 0.02))
      const conversions = Math.floor(clicks * (0.01 + Math.random() * 0.02))
      const spent = Math.floor(clicks * (1 + Math.random() * 1.5))
      const revenue = Math.floor(conversions * (150 + Math.random() * 100))

      data.push({
        date: date.toISOString().split('T')[0],
        impressions,
        clicks,
        conversions,
        spent,
        revenue,
      })
    }

    return data
  }
}

// Mock KPI data
export const mockKPIData: KPIData = {
  totalCampaigns: 24,
  totalSpent: 125000,
  totalImpressions: 2450000,
  totalClicks: 98000,
  totalConversions: 1234,
  averageCTR: 4.2,
  averageCPC: 1.28,
  averageConversionRate: 1.26,
  revenue: 245231,
}



// Generate comprehensive mock data
export const mockClients = MockDataGenerator.generateClients(12)
export const mockCampaigns = MockDataGenerator.generateCampaigns(mockClients, 30)
export const mockChartData = MockDataGenerator.generateAnalyticsData(90)

// Calculate KPI data from generated data
export function calculateKPIData(): KPIData {
  const totals = mockChartData.reduce((acc, curr) => ({
    impressions: acc.impressions + curr.impressions,
    clicks: acc.clicks + curr.clicks,
    conversions: acc.conversions + curr.conversions,
    spent: acc.spent + curr.spent,
    revenue: acc.revenue + curr.revenue,
  }), { impressions: 0, clicks: 0, conversions: 0, spent: 0, revenue: 0 })

  return {
    totalCampaigns: mockCampaigns.filter(c => c.status === 'active').length,
    totalSpent: totals.spent,
    totalImpressions: totals.impressions,
    totalClicks: totals.clicks,
    totalConversions: totals.conversions,
    averageCTR: (totals.clicks / totals.impressions) * 100,
    averageCPC: totals.spent / totals.clicks,
    averageConversionRate: (totals.conversions / totals.clicks) * 100,
    revenue: totals.revenue,
  }
}

// Helper functions to calculate KPI changes
export function calculateKPIChanges() {
  const last30Days = mockChartData.slice(-30)
  const previous30Days = mockChartData.slice(-60, -30)

  const current = last30Days.reduce((acc, curr) => ({
    campaigns: mockCampaigns.filter(c => c.status === 'active').length,
    revenue: acc.revenue + curr.revenue,
    conversions: acc.conversions + curr.conversions,
    clients: mockClients.filter(c => c.status === 'active').length,
  }), { campaigns: 0, revenue: 0, conversions: 0, clients: 0 })

  const previous = previous30Days.reduce((acc, curr) => ({
    campaigns: Math.floor(current.campaigns * 0.9), // Simulate growth
    revenue: acc.revenue + curr.revenue,
    conversions: acc.conversions + curr.conversions,
    clients: Math.floor(current.clients * 0.85), // Simulate growth
  }), { campaigns: 0, revenue: 0, conversions: 0, clients: 0 })

  return {
    campaigns: {
      value: Math.round(((current.campaigns - previous.campaigns) / previous.campaigns) * 100 * 10) / 10,
      period: "from last month",
      isPositive: current.campaigns > previous.campaigns
    },
    revenue: {
      value: Math.round(((current.revenue - previous.revenue) / previous.revenue) * 100 * 10) / 10,
      period: "from last month",
      isPositive: current.revenue > previous.revenue
    },
    conversions: {
      value: Math.round(((current.conversions - previous.conversions) / previous.conversions) * 100 * 10) / 10,
      period: "from last month",
      isPositive: current.conversions > previous.conversions
    },
    clients: {
      value: Math.round(((current.clients - previous.clients) / previous.clients) * 100 * 10) / 10,
      period: "from last month",
      isPositive: current.clients > previous.clients
    },
  }
}

// Function to get recent performance data
export function getRecentPerformance() {
  const last7Days = mockChartData.slice(-7)
  const previous7Days = mockChartData.slice(-14, -7)

  const currentWeekRevenue = last7Days.reduce((sum, day) => sum + day.revenue, 0)
  const previousWeekRevenue = previous7Days.reduce((sum, day) => sum + day.revenue, 0)

  const revenueChange = ((currentWeekRevenue - previousWeekRevenue) / previousWeekRevenue) * 100

  return {
    currentWeekRevenue,
    previousWeekRevenue,
    revenueChange: Math.round(revenueChange * 10) / 10,
    isPositive: revenueChange > 0,
  }
}

// Initialize data service with mock data
import { DataService } from "@/lib/data-service"

// Auto-initialize when module loads
DataService.initializeData(mockCampaigns, mockClients, mockChartData)
