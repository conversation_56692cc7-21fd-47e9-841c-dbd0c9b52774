import { NextRequest, NextResponse } from "next/server"
import { DataService } from "@/lib/data-service"
import { mockCampaigns, mockClients, mockChartData } from "@/data/mock-data"

// POST /api/init - Initialize the data service with mock data
export async function POST(request: NextRequest) {
  try {
    // Initialize the data service
    DataService.initializeData(mockCampaigns, mockClients, mockChartData)
    
    // Get updated KPI data to verify initialization
    const kpiData = await DataService.getKPIData()
    
    return NextResponse.json({
      success: true,
      message: "Data service initialized successfully",
      data: {
        campaignsCount: mockCampaigns.length,
        clientsCount: mockClients.length,
        analyticsCount: mockChartData.length,
        kpiData,
      },
    })
  } catch (error) {
    console.error("Error initializing data service:", error)
    return NextResponse.json(
      { success: false, error: "Failed to initialize data service" },
      { status: 500 }
    )
  }
}

// GET /api/init - Check if data service is initialized
export async function GET(request: NextRequest) {
  try {
    const kpiData = await DataService.getKPIData()
    const isInitialized = kpiData.totalCampaigns > 0
    
    return NextResponse.json({
      success: true,
      data: {
        isInitialized,
        kpiData,
      },
    })
  } catch (error) {
    console.error("Error checking data service status:", error)
    return NextResponse.json(
      { success: false, error: "Failed to check data service status" },
      { status: 500 }
    )
  }
}
