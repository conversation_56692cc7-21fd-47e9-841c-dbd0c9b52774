"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  Dialog<PERSON>ontent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Building2, 
  Mail, 
  Phone, 
  Globe, 
  MapPin, 
  User,
  Calendar,
  Users
} from "lucide-react"
import { Client, CreateClientRequest, UpdateClientRequest } from "@/types"
import { ClientForm } from "@/components/forms/client-form"
import { api } from "@/lib/api-client"
import { formatDate } from "@/lib/utils"
import { toast } from "sonner"

interface ClientModalProps {
  client?: Client
  mode: "create" | "edit" | "view"
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function ClientModal({ 
  client, 
  mode, 
  open, 
  onOpenChange, 
  onSuccess 
}: ClientModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const handleSubmit = async (data: CreateClientRequest | UpdateClientRequest) => {
    setIsLoading(true)
    try {
      if (mode === "create") {
        const response = await api.clients.create(data as CreateClientRequest)
        if (response.success) {
          toast.success("Client created successfully!")
          onOpenChange(false)
          onSuccess?.()
        }
      } else if (mode === "edit" && client) {
        const response = await api.clients.update(client.id, data as UpdateClientRequest)
        if (response.success) {
          toast.success("Client updated successfully!")
          onOpenChange(false)
          onSuccess?.()
        }
      }
    } catch (error) {
      toast.error("Failed to save client. Please try again.")
      console.error("Client save error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!client) return
    
    setIsLoading(true)
    try {
      const response = await api.clients.delete(client.id)
      if (response.success) {
        toast.success("Client deleted successfully!")
        setShowDeleteDialog(false)
        onOpenChange(false)
        onSuccess?.()
      }
    } catch (error) {
      toast.error("Failed to delete client. Please try again.")
      console.error("Client delete error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getModalTitle = () => {
    switch (mode) {
      case "create":
        return "Add New Client"
      case "edit":
        return "Edit Client"
      case "view":
        return "Client Details"
      default:
        return "Client"
    }
  }

  const getModalDescription = () => {
    switch (mode) {
      case "create":
        return "Add a new client to your portfolio with their contact and company information."
      case "edit":
        return "Update client information and contact details."
      case "view":
        return "View client details and contact information."
      default:
        return ""
    }
  }

  if (mode === "view" && client) {
    return (
      <>
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{getModalTitle()}</DialogTitle>
              <DialogDescription>{getModalDescription()}</DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Client Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Client Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="font-semibold text-lg">{client.name}</h3>
                      <p className="text-gray-600 mt-1">{client.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge 
                          className={
                            client.status === "active" 
                              ? "bg-green-100 text-green-800" 
                              : client.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                          }
                        >
                          {client.status}
                        </Badge>
                        <Badge variant="outline">{client.industry}</Badge>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <a href={`mailto:${client.email}`} className="text-sm hover:underline">
                          {client.email}
                        </a>
                      </div>
                      {client.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <a href={`tel:${client.phone}`} className="text-sm hover:underline">
                            {client.phone}
                          </a>
                        </div>
                      )}
                      {client.website && (
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-gray-500" />
                          <a 
                            href={client.website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-sm hover:underline"
                          >
                            {client.website}
                          </a>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          Added {formatDate(client.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium mb-2">Company Details</h4>
                      <div className="space-y-1 text-sm">
                        <div><strong>Company:</strong> {client.company}</div>
                        <div><strong>Industry:</strong> {client.industry}</div>
                        {client.website && (
                          <div>
                            <strong>Website:</strong>{" "}
                            <a 
                              href={client.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {client.website}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                    {client.address && (
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Address
                        </h4>
                        <div className="text-sm space-y-1">
                          {client.address.street && <div>{client.address.street}</div>}
                          <div>
                            {client.address.city && client.address.state && (
                              <span>{client.address.city}, {client.address.state}</span>
                            )}
                            {client.address.zipCode && (
                              <span> {client.address.zipCode}</span>
                            )}
                          </div>
                          {client.address.country && <div>{client.address.country}</div>}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              {client.contactInfo && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Additional Contacts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      {client.contactInfo.primaryContact && (
                        <div>
                          <h4 className="font-medium mb-1">Primary Contact</h4>
                          <div className="text-sm text-gray-600">
                            {client.contactInfo.primaryContact}
                          </div>
                        </div>
                      )}
                      {client.contactInfo.secondaryContact && (
                        <div>
                          <h4 className="font-medium mb-1">Secondary Contact</h4>
                          <div className="text-sm text-gray-600">
                            {client.contactInfo.secondaryContact}
                          </div>
                        </div>
                      )}
                    </div>
                    {client.contactInfo.preferredContactMethod && (
                      <div className="mt-4">
                        <h4 className="font-medium mb-1">Preferred Contact Method</h4>
                        <Badge variant="secondary">
                          {client.contactInfo.preferredContactMethod}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Activity Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Activity Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="text-sm">
                        <span className="font-medium">Client created</span>
                        <span className="text-gray-500 ml-2">
                          {formatDate(client.createdAt)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="text-sm">
                        <span className="font-medium">Last updated</span>
                        <span className="text-gray-500 ml-2">
                          {formatDate(client.updatedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-between">
              <Button
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                Delete Client
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Close
                </Button>
                <Button onClick={() => {
                  onOpenChange(false)
                  // This would trigger edit mode - you'd need to handle this in the parent
                }}>
                  Edit Client
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Client</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete "{client.name}"? This action cannot be undone and will also delete all associated campaigns.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
                disabled={isLoading}
              >
                {isLoading ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getModalTitle()}</DialogTitle>
          <DialogDescription>{getModalDescription()}</DialogDescription>
        </DialogHeader>

        <ClientForm
          client={client}
          onSubmit={handleSubmit}
          onCancel={() => onOpenChange(false)}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  )
}
