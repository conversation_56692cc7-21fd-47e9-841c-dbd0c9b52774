import { NextRequest, NextResponse } from "next/server"
import { DataService } from "@/lib/data-service"
import { CreateCampaignRequest, FilterOptions, SortOptions } from "@/types"

// GET /api/campaigns - Get all campaigns with filtering, sorting, and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || undefined
    const status = searchParams.get("status")?.split(",") || undefined
    const platform = searchParams.get("platform")?.split(",") || undefined
    const clientId = searchParams.get("clientId") || undefined
    const sortField = searchParams.get("sortField") || "updatedAt"
    const sortDirection = (searchParams.get("sortDirection") || "desc") as "asc" | "desc"
    
    // Build filter options
    const filters: FilterOptions = {
      search,
      status,
      platform,
      clientId,
    }
    
    // Add date range filter if provided
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")
    if (startDate && endDate) {
      filters.dateRange = { start: startDate, end: endDate }
    }
    
    // Build sort options
    const sort: SortOptions = {
      field: sortField,
      direction: sortDirection,
    }
    
    // Get campaigns
    const result = await DataService.getCampaigns(filters, sort, page, limit)
    
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
    })
  } catch (error) {
    console.error("Error fetching campaigns:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch campaigns" },
      { status: 500 }
    )
  }
}

// POST /api/campaigns - Create a new campaign
export async function POST(request: NextRequest) {
  try {
    const body: CreateCampaignRequest = await request.json()
    
    // Validate required fields
    if (!body.name || !body.clientId || !body.budget || !body.platform) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      )
    }
    
    // Create campaign
    const campaign = await DataService.createCampaign(body)
    
    return NextResponse.json({
      success: true,
      data: campaign,
      message: "Campaign created successfully",
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating campaign:", error)
    return NextResponse.json(
      { success: false, error: "Failed to create campaign" },
      { status: 500 }
    )
  }
}

// PUT /api/campaigns - Bulk update campaigns
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { campaignIds, updates } = body
    
    if (!campaignIds || !Array.isArray(campaignIds) || !updates) {
      return NextResponse.json(
        { success: false, error: "Invalid request body" },
        { status: 400 }
      )
    }
    
    const updatedCampaigns = []
    for (const id of campaignIds) {
      const campaign = await DataService.updateCampaign(id, updates)
      if (campaign) {
        updatedCampaigns.push(campaign)
      }
    }
    
    return NextResponse.json({
      success: true,
      data: updatedCampaigns,
      message: `${updatedCampaigns.length} campaigns updated successfully`,
    })
  } catch (error) {
    console.error("Error bulk updating campaigns:", error)
    return NextResponse.json(
      { success: false, error: "Failed to update campaigns" },
      { status: 500 }
    )
  }
}

// DELETE /api/campaigns - Bulk delete campaigns
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { campaignIds } = body
    
    if (!campaignIds || !Array.isArray(campaignIds)) {
      return NextResponse.json(
        { success: false, error: "Invalid request body" },
        { status: 400 }
      )
    }
    
    let deletedCount = 0
    for (const id of campaignIds) {
      const deleted = await DataService.deleteCampaign(id)
      if (deleted) {
        deletedCount++
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `${deletedCount} campaigns deleted successfully`,
    })
  } catch (error) {
    console.error("Error bulk deleting campaigns:", error)
    return NextResponse.json(
      { success: false, error: "Failed to delete campaigns" },
      { status: 500 }
    )
  }
}
