"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { ClientsTable } from "@/components/tables/clients-table"
import { ClientModal } from "@/components/modals/client-modal"
import { Client } from "@/types"

export default function ClientsPage() {
  const [selectedClient, setSelectedClient] = useState<Client | undefined>()
  const [modalMode, setModalMode] = useState<"create" | "edit" | "view">("create")
  const [modalOpen, setModalOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleCreateClient = () => {
    setSelectedClient(undefined)
    setModalMode("create")
    setModalOpen(true)
  }

  const handleEditClient = (client: Client) => {
    setSelectedClient(client)
    setModalMode("edit")
    setModalOpen(true)
  }

  const handleViewClient = (client: Client) => {
    setSelectedClient(client)
    setModalMode("view")
    setModalOpen(true)
  }

  const handleDeleteClient = (client: Client) => {
    setSelectedClient(client)
    setModalMode("view")
    setModalOpen(true)
  }

  const handleModalSuccess = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Client Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage your client portfolio and contact information
          </p>
        </div>

        {/* Clients Table */}
        <ClientsTable
          key={refreshKey}
          onCreateClient={handleCreateClient}
          onEditClient={handleEditClient}
          onViewClient={handleViewClient}
          onDeleteClient={handleDeleteClient}
        />

        {/* Client Modal */}
        <ClientModal
          client={selectedClient}
          mode={modalMode}
          open={modalOpen}
          onOpenChange={setModalOpen}
          onSuccess={handleModalSuccess}
        />
      </div>
    </DashboardLayout>
  )
}
