import { NextRequest, NextResponse } from "next/server"
import { DataService } from "@/lib/data-service"
import { UpdateCampaignRequest } from "@/types"

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/campaigns/[id] - Get a specific campaign
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    const campaign = await DataService.getCampaignById(id)
    
    if (!campaign) {
      return NextResponse.json(
        { success: false, error: "Campaign not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: campaign,
    })
  } catch (error) {
    console.error("Error fetching campaign:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch campaign" },
      { status: 500 }
    )
  }
}

// PUT /api/campaigns/[id] - Update a specific campaign
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body: UpdateCampaignRequest = await request.json()
    
    const campaign = await DataService.updateCampaign(id, body)
    
    if (!campaign) {
      return NextResponse.json(
        { success: false, error: "Campaign not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: campaign,
      message: "Campaign updated successfully",
    })
  } catch (error) {
    console.error("Error updating campaign:", error)
    return NextResponse.json(
      { success: false, error: "Failed to update campaign" },
      { status: 500 }
    )
  }
}

// DELETE /api/campaigns/[id] - Delete a specific campaign
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    const deleted = await DataService.deleteCampaign(id)
    
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: "Campaign not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: "Campaign deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting campaign:", error)
    return NextResponse.json(
      { success: false, error: "Failed to delete campaign" },
      { status: 500 }
    )
  }
}
