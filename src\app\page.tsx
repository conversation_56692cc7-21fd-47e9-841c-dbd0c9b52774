import { DashboardLayout } from "@/components/layout/dashboard-layout"
import {
  CampaignsKPICard,
  RevenueKPICard,
  ConversionsKPICard,
  ClientsKPICard
} from "@/components/ui/kpi-card"
import {
  RevenueChart,
  CampaignPerformanceChart,
  ConversionsChart,
  CampaignTrendsChart
} from "@/components/charts/overview-charts"
import { QuickStatsGrid, RecentActivity } from "@/components/ui/quick-stats"
import { calculateKPIData, calculateKPIChanges } from "@/data/mock-data"

export default function Home() {
  const kpiData = calculateKPIData()
  const kpiChanges = calculateKPIChanges()

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Welcome to ADmyBRAND Insights
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            AI-powered analytics dashboard for digital marketing agencies
          </p>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <CampaignsKPICard
            value={kpiData.totalCampaigns}
            change={kpiChanges.campaigns}
          />
          <RevenueKPICard
            value={`$${kpiData.revenue.toLocaleString()}`}
            change={kpiChanges.revenue}
          />
          <ConversionsKPICard
            value={kpiData.totalConversions}
            change={kpiChanges.conversions}
          />
          <ClientsKPICard
            value={12}
            change={kpiChanges.clients}
          />
        </div>

        {/* Charts Section */}
        <div className="grid gap-6 lg:grid-cols-2">
          <RevenueChart />
          <CampaignPerformanceChart />
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          <ConversionsChart />
          <CampaignTrendsChart />
        </div>

        {/* Additional Stats and Activity */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <QuickStatsGrid />
          </div>
          <RecentActivity />
        </div>
      </div>
    </DashboardLayout>
  )
}
