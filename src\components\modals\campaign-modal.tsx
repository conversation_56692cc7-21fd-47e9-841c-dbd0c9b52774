"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  Calendar, 
  DollarSign, 
  Target, 
  Users, 
  TrendingUp, 
  Eye,
  BarChart3,
  MapPin,
  Heart
} from "lucide-react"
import { Campaign, Client, CreateCampaignRequest, UpdateCampaignRequest } from "@/types"
import { CampaignForm } from "@/components/forms/campaign-form"
import { api } from "@/lib/api-client"
import { formatCurrency, formatDate } from "@/lib/utils"
import { toast } from "sonner"

interface CampaignModalProps {
  campaign?: Campaign
  client?: Client
  mode: "create" | "edit" | "view"
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CampaignModal({ 
  campaign, 
  client,
  mode, 
  open, 
  onOpenChange, 
  onSuccess 
}: CampaignModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const handleSubmit = async (data: CreateCampaignRequest | UpdateCampaignRequest) => {
    setIsLoading(true)
    try {
      if (mode === "create") {
        const response = await api.campaigns.create(data as CreateCampaignRequest)
        if (response.success) {
          toast.success("Campaign created successfully!")
          onOpenChange(false)
          onSuccess?.()
        }
      } else if (mode === "edit" && campaign) {
        const response = await api.campaigns.update(campaign.id, data as UpdateCampaignRequest)
        if (response.success) {
          toast.success("Campaign updated successfully!")
          onOpenChange(false)
          onSuccess?.()
        }
      }
    } catch (error) {
      toast.error("Failed to save campaign. Please try again.")
      console.error("Campaign save error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!campaign) return
    
    setIsLoading(true)
    try {
      const response = await api.campaigns.delete(campaign.id)
      if (response.success) {
        toast.success("Campaign deleted successfully!")
        setShowDeleteDialog(false)
        onOpenChange(false)
        onSuccess?.()
      }
    } catch (error) {
      toast.error("Failed to delete campaign. Please try again.")
      console.error("Campaign delete error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getModalTitle = () => {
    switch (mode) {
      case "create":
        return "Create New Campaign"
      case "edit":
        return "Edit Campaign"
      case "view":
        return "Campaign Details"
      default:
        return "Campaign"
    }
  }

  const getModalDescription = () => {
    switch (mode) {
      case "create":
        return "Create a new advertising campaign with targeting and budget settings."
      case "edit":
        return "Update campaign settings and targeting options."
      case "view":
        return "View campaign details and performance metrics."
      default:
        return ""
    }
  }

  if (mode === "view" && campaign) {
    return (
      <>
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{getModalTitle()}</DialogTitle>
              <DialogDescription>{getModalDescription()}</DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Campaign Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Campaign Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="font-semibold text-lg">{campaign.name}</h3>
                      <p className="text-gray-600 mt-1">{campaign.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge>{campaign.platform}</Badge>
                        <Badge variant="outline">{campaign.objective}</Badge>
                        <Badge 
                          className={
                            campaign.status === "active" 
                              ? "bg-green-100 text-green-800" 
                              : campaign.status === "paused"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                          }
                        >
                          {campaign.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          Budget: {formatCurrency(campaign.budget)}
                        </span>
                      </div>
                      {client && (
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">
                            {client.name} ({client.company})
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Performance Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{formatCurrency(campaign.spent || 0)}</div>
                      <div className="text-sm text-gray-500">Spent</div>
                      <div className="text-xs text-gray-400">
                        {((campaign.spent || 0) / campaign.budget * 100).toFixed(1)}% of budget
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{campaign.impressions?.toLocaleString() || 0}</div>
                      <div className="text-sm text-gray-500">Impressions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{campaign.clicks?.toLocaleString() || 0}</div>
                      <div className="text-sm text-gray-500">Clicks</div>
                      <div className="text-xs text-gray-400">
                        {campaign.impressions && campaign.clicks 
                          ? ((campaign.clicks / campaign.impressions) * 100).toFixed(2) + "% CTR"
                          : "0% CTR"
                        }
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{campaign.conversions?.toLocaleString() || 0}</div>
                      <div className="text-sm text-gray-500">Conversions</div>
                      <div className="text-xs text-gray-400">
                        {campaign.clicks && campaign.conversions 
                          ? ((campaign.conversions / campaign.clicks) * 100).toFixed(2) + "% CVR"
                          : "0% CVR"
                        }
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Target Audience */}
              {campaign.targetAudience && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Target Audience
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="font-medium mb-2">Demographics</h4>
                        <div className="space-y-1 text-sm">
                          {campaign.targetAudience.ageRange && (
                            <div>Age: {campaign.targetAudience.ageRange}</div>
                          )}
                          {campaign.targetAudience.gender && (
                            <div>Gender: {campaign.targetAudience.gender}</div>
                          )}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Interests</h4>
                        <div className="flex flex-wrap gap-1">
                          {(campaign.targetAudience.interests || []).map((interest) => (
                            <Badge key={interest} variant="secondary" className="text-xs">
                              {interest}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    {campaign.targetAudience.location && campaign.targetAudience.location.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Target Locations
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {(campaign.targetAudience.location || []).map((location) => (
                            <Badge key={location} variant="outline" className="text-xs">
                              {location}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Ad Sets */}
              {campaign.adSets && campaign.adSets.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Ad Sets</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {(campaign.adSets || []).map((adSet) => (
                        <div key={adSet.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{adSet.name}</h4>
                            <Badge variant="outline">{adSet.status}</Badge>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            Budget: {formatCurrency(adSet.budget)} |
                            Bid: {formatCurrency(adSet.bidAmount)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            <div className="flex justify-between">
              <Button
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                Delete Campaign
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Close
                </Button>
                <Button onClick={() => {
                  onOpenChange(false)
                  // This would trigger edit mode - you'd need to handle this in the parent
                }}>
                  Edit Campaign
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Campaign</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete "{campaign.name}"? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
                disabled={isLoading}
              >
                {isLoading ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getModalTitle()}</DialogTitle>
          <DialogDescription>{getModalDescription()}</DialogDescription>
        </DialogHeader>

        <CampaignForm
          campaign={campaign}
          onSubmit={handleSubmit}
          onCancel={() => onOpenChange(false)}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  )
}
