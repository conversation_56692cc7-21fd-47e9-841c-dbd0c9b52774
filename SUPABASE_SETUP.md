# Supabase Setup Guide for ADmyBRAND Insights

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up for a free account  
2. Click "New Project" and create a new project
3. Choose a name for your project (e.g., "admybrand-insights")
4. Set a strong database password
5. Choose a region close to your users
6. Wait for the project to be created (this takes a few minutes)

## 2. Get Your Project Credentials

1. In your Supabase dashboard, go to Settings > API
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-ref.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **Service role key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## 3. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

## 4. Set Up the Database Schema

1. In your Supabase dashboard, go to the SQL Editor
2. Copy the contents of `supabase-schema.sql` from your project root
3. Paste it into the SQL Editor and click "Run"
4. This will create all the necessary tables, policies, and triggers

## 5. Configure Authentication

1. In your Supabase dashboard, go to Authentication > Settings
2. Under "Site URL", add your development URL: `http://localhost:3000`
3. Under "Redirect URLs", add: `http://localhost:3000/auth`
4. Enable email confirmations if desired (optional for development)

## 6. Test the Setup

1. Start your development server: `npm run dev`
2. Go to `http://localhost:3000/auth`
3. Try creating a new account
4. Check your Supabase dashboard to see if the user was created
5. Try logging in and creating a client to test the database integration

## 7. Optional: Enable Real-time Features

If you want real-time updates (data changes appear instantly across all users):

1. In your Supabase dashboard, go to Database > Replication
2. Enable replication for the `clients` and `campaigns` tables
3. The app will automatically use real-time subscriptions

## Troubleshooting

### Common Issues:

1. **"Invalid JWT" errors**: Check that your environment variables are correct
2. **"Row Level Security" errors**: Make sure you ran the schema SQL completely
3. **Connection errors**: Verify your project URL is correct and the project is active
4. **Auth errors**: Check that your redirect URLs are configured correctly

### Development Notes:

- The app currently uses a mock user ID for development when no auth token is present
- In production, you should remove the mock user fallback in the API routes
- All data is isolated per user thanks to Row Level Security policies

## Next Steps

Once Supabase is configured:
1. All client and campaign data will be stored in your Supabase database
2. User authentication will work with email/password
3. Data will persist between sessions
4. Multiple users can use the app with isolated data

The app is now fully integrated with Supabase for production use!
