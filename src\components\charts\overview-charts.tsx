"use client"

import React from "react"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts"
import { Card } from "@/components/ui/card"
import { motion } from "framer-motion"

// Sample data for charts
const revenueData = [
  { month: "Jan", revenue: 12000, campaigns: 8, conversions: 245 },
  { month: "Feb", revenue: 15000, campaigns: 12, conversions: 312 },
  { month: "Mar", revenue: 18000, campaigns: 15, conversions: 398 },
  { month: "Apr", revenue: 22000, campaigns: 18, conversions: 456 },
  { month: "May", revenue: 28000, campaigns: 22, conversions: 567 },
  { month: "Jun", revenue: 32000, campaigns: 24, conversions: 634 },
]

const campaignPerformanceData = [
  { name: "Google Ads", value: 35, color: "#8b5cf6" },
  { name: "Facebook", value: 28, color: "#3b82f6" },
  { name: "Instagram", value: 20, color: "#ef4444" },
  { name: "LinkedIn", value: 12, color: "#10b981" },
  { name: "Others", value: 5, color: "#f59e0b" },
]

const conversionData = [
  { day: "Mon", conversions: 45, clicks: 1200 },
  { day: "Tue", conversions: 52, clicks: 1350 },
  { day: "Wed", conversions: 38, clicks: 1100 },
  { day: "Thu", conversions: 61, clicks: 1450 },
  { day: "Fri", conversions: 55, clicks: 1380 },
  { day: "Sat", conversions: 42, clicks: 1050 },
  { day: "Sun", conversions: 48, clicks: 1200 },
]

interface ChartCardProps {
  title: string
  children: React.ReactNode
  className?: string
}

function ChartCard({ title, children, className }: ChartCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="p-6 bg-white/80 backdrop-blur dark:bg-gray-900/80 border-0 shadow-lg">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {title}
        </h3>
        {children}
      </Card>
    </motion.div>
  )
}

export function RevenueChart() {
  return (
    <ChartCard title="Revenue Trend">
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={revenueData}>
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
          <XAxis 
            dataKey="month" 
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis 
            stroke="#64748b"
            fontSize={12}
            tickFormatter={(value) => `$${value / 1000}K`}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "8px",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value: number) => [`$${value.toLocaleString()}`, "Revenue"]}
          />
          <Area
            type="monotone"
            dataKey="revenue"
            stroke="#8b5cf6"
            strokeWidth={2}
            fill="url(#revenueGradient)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

export function CampaignPerformanceChart() {
  return (
    <ChartCard title="Campaign Performance by Platform">
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={campaignPerformanceData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={100}
            paddingAngle={5}
            dataKey="value"
          >
            {campaignPerformanceData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "8px",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value: number) => [`${value}%`, "Performance"]}
          />
        </PieChart>
      </ResponsiveContainer>
      <div className="mt-4 grid grid-cols-2 gap-2">
        {campaignPerformanceData.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: item.color }}
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {item.name}: {item.value}%
            </span>
          </div>
        ))}
      </div>
    </ChartCard>
  )
}

export function ConversionsChart() {
  return (
    <ChartCard title="Weekly Conversions">
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={conversionData}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
          <XAxis 
            dataKey="day" 
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis 
            stroke="#64748b"
            fontSize={12}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "8px",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
            }}
          />
          <Bar 
            dataKey="conversions" 
            fill="#8b5cf6"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

export function CampaignTrendsChart() {
  return (
    <ChartCard title="Campaign & Conversion Trends">
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={revenueData}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
          <XAxis 
            dataKey="month" 
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis 
            stroke="#64748b"
            fontSize={12}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "8px",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
            }}
          />
          <Line
            type="monotone"
            dataKey="campaigns"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
            name="Campaigns"
          />
          <Line
            type="monotone"
            dataKey="conversions"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
            name="Conversions"
          />
        </LineChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}
