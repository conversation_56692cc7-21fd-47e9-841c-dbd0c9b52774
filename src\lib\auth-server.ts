import { createServerClient } from '@supabase/ssr'
import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { isSupabaseConfigured } from '@/lib/supabase'

export async function getServerUser(request?: NextRequest) {
  if (!isSupabaseConfigured) {
    // For development without Supa<PERSON>, return a mock user
    return { id: 'mock-user-id', email: '<EMAIL>' }
  }

  const cookieStore = cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          // Can't set cookies in API routes, but this is required by the interface
        },
        remove(name: string, options: any) {
          // Can't remove cookies in API routes, but this is required by the interface
        },
      },
    }
  )

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser()

  if (error || !user) {
    throw new Error('Authentication required')
  }

  return user
}

export async function requireAuth(request?: NextRequest) {
  const user = await getServerUser(request)
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}
