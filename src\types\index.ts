export interface Campaign {
  id: string
  name: string
  clientId: string
  status: 'active' | 'paused' | 'completed' | 'draft'
  budget: number
  spent: number
  impressions: number
  clicks: number
  conversions: number
  ctr: number
  cpc: number
  cpm?: number
  conversionRate: number
  startDate: string
  endDate: string
  platform: 'Google Ads' | 'Facebook' | 'Instagram' | 'LinkedIn' | 'Twitter' | 'TikTok'
  objective: 'awareness' | 'traffic' | 'engagement' | 'leads' | 'sales' | 'app_installs'
  targetAudience?: {
    ageRange: string
    gender: string
    location: string[]
    interests: string[]
  }
  adSets?: AdSet[]
  createdAt: string
  updatedAt: string
}

export interface AdSet {
  id: string
  campaignId: string
  name: string
  budget: number
  spent: number
  impressions: number
  clicks: number
  conversions: number
  status: 'active' | 'paused'
  createdAt: string
  updatedAt: string
}

export interface Client {
  id: string
  name: string
  email: string
  phone?: string
  company: string
  industry: string
  status: 'active' | 'inactive' | 'pending'
  totalBudget: number
  totalSpent: number
  totalCampaigns: number
  averageROI: number
  avatar?: string
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  contactPerson?: {
    name: string
    email: string
    phone: string
    role: string
  }
  createdAt: string
  updatedAt: string
}

export interface KPIData {
  totalCampaigns: number
  totalSpent: number
  totalImpressions: number
  totalClicks: number
  totalConversions: number
  averageCTR: number
  averageCPC: number
  averageConversionRate: number
  revenue: number
}

export interface ChartData {
  date: string
  impressions: number
  clicks: number
  conversions: number
  spent: number
  revenue: number
}

export interface AIInsight {
  id: string
  type: 'trend' | 'recommendation' | 'alert' | 'opportunity'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  actionable: boolean
  relatedCampaigns?: string[]
  createdAt: string
}

export interface DashboardFilters {
  dateRange: {
    from: Date
    to: Date
  }
  clients: string[]
  platforms: string[]
  status: string[]
}

export interface ExportOptions {
  format: 'csv' | 'pdf'
  data: 'campaigns' | 'clients' | 'analytics'
  filters?: DashboardFilters
}

export interface NotificationToast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  description?: string
  duration?: number
}

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'admin' | 'manager' | 'analyst'
  preferences: {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    defaultDateRange: number
  }
}

export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasNext: boolean
  hasPrev: boolean
}

// API Request/Response types
export interface CreateCampaignRequest {
  name: string
  clientId: string
  budget: number
  startDate: string
  endDate: string
  platform: Campaign['platform']
  objective: Campaign['objective']
  targetAudience?: Campaign['targetAudience']
}

export interface UpdateCampaignRequest extends Partial<CreateCampaignRequest> {
  status?: Campaign['status']
}

export interface CreateClientRequest {
  name: string
  email: string
  phone?: string
  company: string
  industry: string
  totalBudget: number
  address?: Client['address']
  contactPerson?: Client['contactPerson']
}

export interface UpdateClientRequest extends Partial<CreateClientRequest> {
  status?: Client['status']
}

export interface AnalyticsQuery {
  startDate: string
  endDate: string
  clientIds?: string[]
  campaignIds?: string[]
  platforms?: string[]
  groupBy?: 'day' | 'week' | 'month'
  metrics?: string[]
}

export interface AnalyticsData {
  period: string
  metrics: {
    impressions: number
    clicks: number
    conversions: number
    spent: number
    revenue: number
    ctr: number
    cpc: number
    conversionRate: number
    roas: number
  }
  campaigns?: Campaign[]
  clients?: Client[]
}

// Database/Storage types
export interface DatabaseConfig {
  campaigns: Campaign[]
  clients: Client[]
  analytics: ChartData[]
  insights: AIInsight[]
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface FilterOptions {
  search?: string
  status?: string[]
  platform?: string[]
  clientId?: string
  dateRange?: {
    start: string
    end: string
  }
}
