import { NextRequest, NextResponse } from "next/server"
import { SupabaseService } from "@/lib/supabase-service"
import { isSupabaseConfigured } from "@/lib/supabase"
import { DataService } from "@/lib/data-service"
import { UpdateClientRequest } from "@/types"
import { getServerUser } from "@/lib/auth-server"

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/clients/[id] - Get a specific client
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    let client
    if (isSupabaseConfigured) {
      const user = await getServerUser(request)
      client = await SupabaseService.getClientById(id, user.id)
    } else {
      client = await DataService.getClientById(id)
    }
    
    if (!client) {
      return NextResponse.json(
        { success: false, error: "Client not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: client,
    })
  } catch (error) {
    console.error("Error fetching client:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch client" },
      { status: 500 }
    )
  }
}

// PUT /api/clients/[id] - Update a specific client
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body: UpdateClientRequest = await request.json()
    
    let client
    if (isSupabaseConfigured) {
      const user = await getServerUser(request)
      client = await SupabaseService.updateClient(id, body, user.id)
    } else {
      client = await DataService.updateClient(id, body)
    }
    
    if (!client) {
      return NextResponse.json(
        { success: false, error: "Client not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: client,
      message: "Client updated successfully",
    })
  } catch (error) {
    console.error("Error updating client:", error)
    return NextResponse.json(
      { success: false, error: "Failed to update client" },
      { status: 500 }
    )
  }
}

// DELETE /api/clients/[id] - Delete a specific client
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    
    if (isSupabaseConfigured) {
      const user = await getServerUser(request)
      await SupabaseService.deleteClient(id, user.id)
    } else {
      const deleted = await DataService.deleteClient(id)
      if (!deleted) {
        return NextResponse.json(
          { success: false, error: "Client not found" },
          { status: 404 }
        )
      }
    }
    

    
    return NextResponse.json({
      success: true,
      message: "Client deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting client:", error)
    return NextResponse.json(
      { success: false, error: "Failed to delete client" },
      { status: 500 }
    )
  }
}
