"use client"

import React from "react"
import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  Users, 
  MousePointer,
  Eye,
  Zap
} from "lucide-react"

interface QuickStatProps {
  title: string
  value: string
  change?: {
    value: number
    isPositive: boolean
  }
  icon: React.ReactNode
  color: string
}

function QuickStat({ title, value, change, icon, color }: QuickStatProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50"
    >
      <div className={`p-2 rounded-lg ${color}`}>
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {title}
        </p>
        <div className="flex items-center space-x-2">
          <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {value}
          </p>
          {change && (
            <Badge 
              variant={change.isPositive ? "success" : "destructive"}
              className="text-xs"
            >
              {change.isPositive ? (
                <TrendingUp className="w-3 h-3 mr-1" />
              ) : (
                <TrendingDown className="w-3 h-3 mr-1" />
              )}
              {Math.abs(change.value)}%
            </Badge>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export function QuickStatsGrid() {
  const stats = [
    {
      title: "Avg. CTR",
      value: "4.2%",
      change: { value: 0.3, isPositive: true },
      icon: <MousePointer className="w-4 h-4 text-white" />,
      color: "bg-blue-500",
    },
    {
      title: "Avg. CPC",
      value: "$1.28",
      change: { value: 0.15, isPositive: false },
      icon: <DollarSign className="w-4 h-4 text-white" />,
      color: "bg-green-500",
    },
    {
      title: "Conv. Rate",
      value: "1.26%",
      change: { value: 0.08, isPositive: true },
      icon: <Target className="w-4 h-4 text-white" />,
      color: "bg-purple-500",
    },
    {
      title: "Impressions",
      value: "2.45M",
      change: { value: 12.5, isPositive: true },
      icon: <Eye className="w-4 h-4 text-white" />,
      color: "bg-orange-500",
    },
    {
      title: "Total Clicks",
      value: "98K",
      change: { value: 8.7, isPositive: true },
      icon: <Zap className="w-4 h-4 text-white" />,
      color: "bg-red-500",
    },
    {
      title: "Active Users",
      value: "1,234",
      change: { value: 5.2, isPositive: true },
      icon: <Users className="w-4 h-4 text-white" />,
      color: "bg-indigo-500",
    },
  ]

  return (
    <Card className="p-6 bg-white/80 backdrop-blur dark:bg-gray-900/80 border-0 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Quick Stats
        </h3>
        <Badge variant="outline" className="text-xs">
          Last 30 days
        </Badge>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <QuickStat key={index} {...stat} />
        ))}
      </div>
    </Card>
  )
}

export function RecentActivity() {
  const activities = [
    {
      action: "New campaign created",
      campaign: "Summer Sale 2024",
      time: "2 hours ago",
      type: "success",
    },
    {
      action: "Budget threshold reached",
      campaign: "Brand Awareness Q1",
      time: "4 hours ago",
      type: "warning",
    },
    {
      action: "Campaign paused",
      campaign: "Product Launch",
      time: "6 hours ago",
      type: "info",
    },
    {
      action: "High conversion rate detected",
      campaign: "Holiday Special",
      time: "8 hours ago",
      type: "success",
    },
    {
      action: "New client onboarded",
      campaign: "TechStart Inc.",
      time: "1 day ago",
      type: "success",
    },
  ]

  const getActivityColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
      case "info":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
    }
  }

  return (
    <Card className="p-6 bg-white/80 backdrop-blur dark:bg-gray-900/80 border-0 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Recent Activity
        </h3>
        <Badge variant="outline" className="text-xs">
          Live
        </Badge>
      </div>
      <div className="space-y-3">
        {activities.map((activity, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          >
            <div className={`w-2 h-2 rounded-full ${getActivityColor(activity.type).split(' ')[0]}`} />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {activity.action}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {activity.campaign}
              </p>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {activity.time}
            </span>
          </motion.div>
        ))}
      </div>
    </Card>
  )
}
