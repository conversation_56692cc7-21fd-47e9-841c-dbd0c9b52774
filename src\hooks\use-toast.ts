"use client"

import { useState, useCallback } from "react"
import { NotificationToast } from "@/types"
import { generateId } from "@/lib/utils"

export function useToast() {
  const [toasts, setToasts] = useState<NotificationToast[]>([])

  const addToast = useCallback((
    toast: Omit<NotificationToast, "id">
  ) => {
    const id = generateId()
    const newToast: NotificationToast = {
      id,
      duration: 5000,
      ...toast,
    }

    setToasts((prev) => [...prev, newToast])
    return id
  }, [])

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  const success = useCallback((title: string, description?: string) => {
    return addToast({ type: "success", title, description })
  }, [addToast])

  const error = useCallback((title: string, description?: string) => {
    return addToast({ type: "error", title, description })
  }, [addToast])

  const warning = useCallback((title: string, description?: string) => {
    return addToast({ type: "warning", title, description })
  }, [addToast])

  const info = useCallback((title: string, description?: string) => {
    return addToast({ type: "info", title, description })
  }, [addToast])

  return {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info,
  }
}
